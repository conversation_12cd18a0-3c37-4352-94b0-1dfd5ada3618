/* News page specific styles */
.newsPage {
  background-color: #f9fafb;
  min-height: 100vh;
}

.pageHeader {
  background: white;
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1);
}

.headerContent {
  max-width: 1200px;
  margin: 0 auto;
  padding: 2rem 1rem;
}

.pageTitle {
  font-size: 1.875rem;
  font-weight: 700;
  color: #1f2937;
  margin-bottom: 0.5rem;
}

.pageSubtitle {
  color: #4b5563;
}

.mainContent {
  max-width: 1200px;
  margin: 0 auto;
  padding: 2rem 1rem;
  display: grid;
  grid-template-columns: 3fr 1fr;
  gap: 2rem;
}

.contentArea {
  /* Main content styles */
}

.sidebar {
  /* Sidebar styles */
}

.featuredSection {
  margin-bottom: 3rem;
}

.featuredTitle {
  font-size: 1.5rem;
  font-weight: 700;
  margin-bottom: 1.5rem;
  color: #1f2937;
}

.featuredCard {
  background: white;
  border-radius: 0.5rem;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
  overflow: hidden;
  transition: all 0.3s ease;
}

.featuredCard:hover {
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
}

.featuredImage {
  width: 100%;
  height: 16rem;
  object-fit: cover;
}

.featuredContent {
  padding: 1.5rem;
}

.categoryTags {
  display: flex;
  align-items: center;
  gap: 1rem;
  margin-bottom: 0.75rem;
}

.categoryTag {
  background: #dbeafe;
  color: #2563eb;
  padding: 0.25rem 0.75rem;
  border-radius: 9999px;
  font-size: 0.875rem;
  font-weight: 600;
}

.newsDate {
  color: #6b7280;
  font-size: 0.875rem;
}

.featuredNewsTitle {
  font-size: 1.5rem;
  font-weight: 700;
  margin-bottom: 0.75rem;
  color: #1f2937;
  cursor: pointer;
  transition: color 0.3s ease;
}

.featuredNewsTitle:hover {
  color: #2563eb;
}

.newsExcerpt {
  color: #4b5563;
  line-height: 1.625;
  margin-bottom: 1rem;
}

.readMoreBtn {
  color: #2563eb;
  font-weight: 600;
  text-decoration: none;
  transition: color 0.3s ease;
}

.readMoreBtn:hover {
  color: #1d4ed8;
}

.filterSection {
  margin-bottom: 2rem;
}

.filterButtons {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
}

.filterBtn {
  padding: 0.5rem 1rem;
  border-radius: 9999px;
  font-size: 0.875rem;
  font-weight: 600;
  transition: all 0.3s ease;
  border: none;
  cursor: pointer;
}

.filterBtn.active {
  background: #2563eb;
  color: white;
}

.filterBtn:not(.active) {
  background: white;
  color: #4b5563;
  border: 1px solid #d1d5db;
}

.filterBtn:not(.active):hover {
  background: #eff6ff;
  color: #2563eb;
}

.newsGrid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
  gap: 1.5rem;
  margin-bottom: 3rem;
}

.newsCard {
  background: white;
  border-radius: 0.5rem;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
  overflow: hidden;
  transition: all 0.3s ease;
}

.newsCard:hover {
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
  transform: translateY(-2px);
}

.newsImage {
  width: 100%;
  height: 12rem;
  object-fit: cover;
}

.newsContent {
  padding: 1.5rem;
}

.newsTitle {
  font-size: 1.125rem;
  font-weight: 600;
  margin: 0.75rem 0;
  color: #1f2937;
  cursor: pointer;
  transition: color 0.3s ease;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.newsTitle:hover {
  color: #2563eb;
}

.newsCardExcerpt {
  color: #4b5563;
  font-size: 0.875rem;
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.pagination {
  display: flex;
  justify-content: center;
  margin-top: 3rem;
}

.paginationButtons {
  display: flex;
  gap: 0.5rem;
}

.paginationBtn {
  padding: 0.5rem 1rem;
  background: white;
  border: 1px solid #d1d5db;
  border-radius: 0.5rem;
  cursor: pointer;
  transition: all 0.3s ease;
}

.paginationBtn:hover {
  background: #f9fafb;
}

.paginationBtn.active {
  background: #2563eb;
  color: white;
  border-color: #2563eb;
}

.sidebarCard {
  background: white;
  border-radius: 0.5rem;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
  padding: 1.5rem;
  margin-bottom: 1.5rem;
}

.sidebarTitle {
  font-size: 1.125rem;
  font-weight: 600;
  margin-bottom: 1rem;
  color: #1f2937;
}

.searchInput {
  width: 100%;
  padding: 0.75rem 1rem;
  border: 1px solid #d1d5db;
  border-radius: 0.5rem;
  outline: none;
  transition: all 0.3s ease;
}

.searchInput:focus {
  border-color: #2563eb;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.searchButton {
  position: absolute;
  right: 0.5rem;
  top: 50%;
  transform: translateY(-50%);
  background: none;
  border: none;
  color: #6b7280;
  cursor: pointer;
  transition: color 0.3s ease;
}

.searchButton:hover {
  color: #2563eb;
}

.popularNews {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.popularItem {
  display: flex;
  gap: 0.75rem;
}

.popularImage {
  width: 4rem;
  height: 4rem;
  object-fit: cover;
  border-radius: 0.25rem;
  flex-shrink: 0;
}

.popularContent {
  flex: 1;
}

.popularTitle {
  font-size: 0.875rem;
  font-weight: 600;
  color: #1f2937;
  cursor: pointer;
  transition: color 0.3s ease;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  margin-bottom: 0.25rem;
}

.popularTitle:hover {
  color: #2563eb;
}

.popularDate {
  font-size: 0.75rem;
  color: #6b7280;
}

/* Responsive design */
@media (max-width: 1024px) {
  .mainContent {
    grid-template-columns: 1fr;
    gap: 2rem;
  }
  
  .newsGrid {
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  }
}

@media (max-width: 768px) {
  .headerContent {
    padding: 1.5rem 1rem;
  }
  
  .mainContent {
    padding: 1.5rem 1rem;
  }
  
  .newsGrid {
    grid-template-columns: 1fr;
  }
  
  .filterButtons {
    justify-content: center;
  }
  
  .pagination {
    overflow-x: auto;
    padding: 0 1rem;
  }
}
