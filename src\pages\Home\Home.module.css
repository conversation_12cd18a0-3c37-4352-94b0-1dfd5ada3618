/* Home page specific styles */
.hero {
  background: linear-gradient(135deg, #2563eb 0%, #1e40af 100%);
  color: white;
  padding: 5rem 0;
  text-align: center;
}

.heroTitle {
  font-size: 3.75rem;
  font-weight: 700;
  margin-bottom: 1.5rem;
  line-height: 1.1;
}

.heroSubtitle {
  font-size: 1.25rem;
  margin-bottom: 2rem;
  max-width: 48rem;
  margin-left: auto;
  margin-right: auto;
  opacity: 0.9;
}

.heroButtons {
  display: flex;
  gap: 1rem;
  justify-content: center;
  flex-wrap: wrap;
}

.primaryButton {
  background-color: white;
  color: #2563eb;
  padding: 0.75rem 2rem;
  border-radius: 0.5rem;
  font-weight: 600;
  text-decoration: none;
  transition: all 0.3s ease;
  border: none;
  cursor: pointer;
}

.primaryButton:hover {
  background-color: #f3f4f6;
  transform: translateY(-2px);
}

.secondaryButton {
  border: 2px solid white;
  color: white;
  background: transparent;
  padding: 0.75rem 2rem;
  border-radius: 0.5rem;
  font-weight: 600;
  text-decoration: none;
  transition: all 0.3s ease;
  cursor: pointer;
}

.secondaryButton:hover {
  background-color: white;
  color: #2563eb;
}

.quickServices {
  padding: 4rem 0;
  background-color: #f9fafb;
}

.sectionTitle {
  font-size: 1.875rem;
  font-weight: 700;
  text-align: center;
  margin-bottom: 3rem;
  color: #1f2937;
}

.servicesGrid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 1.5rem;
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 1rem;
}

.serviceCard {
  background: white;
  padding: 1.5rem;
  border-radius: 0.5rem;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  text-decoration: none;
  color: inherit;
}

.serviceCard:hover {
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
  transform: translateY(-4px);
}

.serviceIcon {
  font-size: 2.5rem;
  margin-bottom: 1rem;
  display: block;
}

.serviceTitle {
  font-size: 1.125rem;
  font-weight: 600;
  margin-bottom: 0.5rem;
  color: #1f2937;
}

.serviceDescription {
  color: #4b5563;
  font-size: 0.875rem;
  line-height: 1.5;
}

.latestNews {
  padding: 4rem 0;
  background: white;
}

.newsHeader {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 3rem;
  max-width: 1200px;
  margin-left: auto;
  margin-right: auto;
  padding: 0 1rem;
}

.viewAllLink {
  color: #2563eb;
  text-decoration: none;
  font-weight: 600;
  transition: color 0.3s ease;
}

.viewAllLink:hover {
  color: #1d4ed8;
}

.newsGrid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
  gap: 2rem;
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 1rem;
}

.newsCard {
  background: #f9fafb;
  border-radius: 0.5rem;
  overflow: hidden;
  transition: all 0.3s ease;
}

.newsCard:hover {
  transform: translateY(-2px);
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
}

.newsImage {
  width: 100%;
  height: 12rem;
  object-fit: cover;
}

.newsContent {
  padding: 1.5rem;
}

.newsDate {
  font-size: 0.875rem;
  color: #2563eb;
  font-weight: 600;
}

.newsTitle {
  font-size: 1.125rem;
  font-weight: 600;
  margin: 0.5rem 0 0.75rem 0;
  color: #1f2937;
}

.newsExcerpt {
  color: #4b5563;
  font-size: 0.875rem;
  line-height: 1.5;
}

.statistics {
  padding: 4rem 0;
  background: #2563eb;
  color: white;
}

.statsGrid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 2rem;
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 1rem;
  text-align: center;
}

.statNumber {
  font-size: 2.25rem;
  font-weight: 700;
  margin-bottom: 0.5rem;
}

.statLabel {
  color: #bfdbfe;
  font-size: 0.875rem;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .heroTitle {
    font-size: 2.5rem;
  }
  
  .heroSubtitle {
    font-size: 1.125rem;
  }
  
  .heroButtons {
    flex-direction: column;
    align-items: center;
  }
  
  .primaryButton,
  .secondaryButton {
    width: 200px;
  }
  
  .newsHeader {
    flex-direction: column;
    gap: 1rem;
    text-align: center;
  }
  
  .servicesGrid,
  .newsGrid {
    grid-template-columns: 1fr;
  }
  
  .statsGrid {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (max-width: 480px) {
  .hero {
    padding: 3rem 0;
  }
  
  .quickServices,
  .latestNews,
  .statistics {
    padding: 2.5rem 0;
  }
  
  .statsGrid {
    grid-template-columns: 1fr;
  }
}
