/* Contact page specific styles */
.contactPage {
  background-color: #f9fafb;
  min-height: 100vh;
}

.pageHeader {
  background: white;
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1);
}

.headerContent {
  max-width: 1200px;
  margin: 0 auto;
  padding: 2rem 1rem;
}

.pageTitle {
  font-size: 1.875rem;
  font-weight: 700;
  color: #1f2937;
  margin-bottom: 0.5rem;
}

.pageSubtitle {
  color: #4b5563;
}

.mainContent {
  max-width: 1200px;
  margin: 0 auto;
  padding: 2rem 1rem;
  display: grid;
  grid-template-columns: 1fr 2fr;
  gap: 2rem;
}

.contactInfo {
  /* Contact info styles */
}

.formSection {
  /* Form section styles */
}

.infoCard {
  background: white;
  border-radius: 0.5rem;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
  padding: 1.5rem;
  margin-bottom: 1.5rem;
}

.infoTitle {
  font-size: 1.25rem;
  font-weight: 600;
  margin-bottom: 1.5rem;
  color: #1f2937;
}

.contactItems {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.contactItem {
  display: flex;
  align-items: flex-start;
  gap: 1rem;
}

.contactIcon {
  background: #dbeafe;
  padding: 0.75rem;
  border-radius: 0.5rem;
  flex-shrink: 0;
}

.contactIcon span {
  color: #2563eb;
  font-size: 1.25rem;
}

.contactDetails h3 {
  font-weight: 600;
  color: #1f2937;
  margin-bottom: 0.25rem;
}

.contactDetails p {
  color: #4b5563;
  font-size: 0.875rem;
  line-height: 1.5;
}

.quickSupport {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.supportButton {
  width: 100%;
  padding: 0.75rem;
  border-radius: 0.5rem;
  border: none;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
}

.supportPrimary {
  background: #059669;
  color: white;
}

.supportPrimary:hover {
  background: #047857;
}

.supportSecondary {
  background: #2563eb;
  color: white;
}

.supportSecondary:hover {
  background: #1d4ed8;
}

.supportTertiary {
  border: 1px solid #d1d5db;
  color: #4b5563;
  background: white;
}

.supportTertiary:hover {
  background: #f9fafb;
}

.formCard {
  background: white;
  border-radius: 0.5rem;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
  padding: 1.5rem;
}

.formTitle {
  font-size: 1.25rem;
  font-weight: 600;
  margin-bottom: 1.5rem;
  color: #1f2937;
}

.contactForm {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.formRow {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 1.5rem;
}

.formGroup {
  display: flex;
  flex-direction: column;
}

.formLabel {
  display: block;
  font-size: 0.875rem;
  font-weight: 600;
  color: #374151;
  margin-bottom: 0.5rem;
}

.required {
  color: #dc2626;
}

.formInput {
  width: 100%;
  padding: 0.75rem 1rem;
  border: 1px solid #d1d5db;
  border-radius: 0.5rem;
  outline: none;
  transition: all 0.3s ease;
}

.formInput:focus {
  border-color: #2563eb;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.formSelect {
  width: 100%;
  padding: 0.75rem 1rem;
  border: 1px solid #d1d5db;
  border-radius: 0.5rem;
  outline: none;
  transition: all 0.3s ease;
  background: white;
}

.formSelect:focus {
  border-color: #2563eb;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.formTextarea {
  width: 100%;
  padding: 0.75rem 1rem;
  border: 1px solid #d1d5db;
  border-radius: 0.5rem;
  outline: none;
  transition: all 0.3s ease;
  resize: none;
  min-height: 150px;
}

.formTextarea:focus {
  border-color: #2563eb;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.checkboxGroup {
  display: flex;
  align-items: flex-start;
  gap: 0.75rem;
}

.checkbox {
  width: 1rem;
  height: 1rem;
  border: 1px solid #d1d5db;
  border-radius: 0.25rem;
  flex-shrink: 0;
  margin-top: 0.125rem;
}

.checkbox:focus {
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.checkboxLabel {
  font-size: 0.875rem;
  color: #4b5563;
  line-height: 1.5;
}

.checkboxLabel a {
  color: #2563eb;
  text-decoration: none;
}

.checkboxLabel a:hover {
  color: #1d4ed8;
  text-decoration: underline;
}

.submitButton {
  width: 100%;
  background: #2563eb;
  color: white;
  padding: 0.75rem;
  border-radius: 0.5rem;
  border: none;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
}

.submitButton:hover {
  background: #1d4ed8;
}

.mapSection {
  margin-top: 3rem;
}

.mapCard {
  background: white;
  border-radius: 0.5rem;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
  padding: 1.5rem;
}

.mapTitle {
  font-size: 1.25rem;
  font-weight: 600;
  margin-bottom: 1.5rem;
  color: #1f2937;
}

.mapPlaceholder {
  background: #e5e7eb;
  height: 24rem;
  border-radius: 0.5rem;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #6b7280;
}

/* Responsive design */
@media (max-width: 1024px) {
  .mainContent {
    grid-template-columns: 1fr;
    gap: 2rem;
  }
}

@media (max-width: 768px) {
  .headerContent {
    padding: 1.5rem 1rem;
  }
  
  .mainContent {
    padding: 1.5rem 1rem;
  }
  
  .formRow {
    grid-template-columns: 1fr;
  }
  
  .contactItem {
    flex-direction: column;
    text-align: center;
  }
  
  .contactIcon {
    align-self: center;
  }
}
