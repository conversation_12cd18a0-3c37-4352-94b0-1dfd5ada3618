import React, { useState } from 'react';
import styles from './News.module.css';

const News = () => {
  const [selectedCategory, setSelectedCategory] = useState('all');

  const categories = [
    { id: 'all', name: 'Tất cả' },
    { id: 'announcement', name: 'Thông báo' },
    { id: 'policy', name: '<PERSON><PERSON><PERSON> sách' },
    { id: 'service', name: 'Dịch vụ' },
    { id: 'event', name: 'S<PERSON> kiện' }
  ];

  const newsData = [
    {
      id: 1,
      title: 'Thông báo về việc triển khai dịch vụ công trực tuyến mới',
      excerpt: 'Từ ngày 20/07/2024, hệ thống sẽ triển khai các dịch vụ công trực tuyến mới nhằm phục vụ người dân tốt hơn...',
      date: '15/07/2024',
      category: 'announcement',
      image: 'https://via.placeholder.com/400x200',
      featured: true
    },
    {
      id: 2,
      title: '<PERSON><PERSON>ớng dẫn sử dụng dịch vụ công trực tuyến',
      excerpt: '<PERSON><PERSON> giúp người dân sử dụng dịch vụ công trực tuyến một cách hiệu quả, chúng tôi cung cấp hướng dẫn chi tiết...',
      date: '14/07/2024',
      category: 'service',
      image: 'https://via.placeholder.com/400x200'
    },
    {
      id: 3,
      title: 'Cập nhật danh sách thủ tục hành chính',
      excerpt: 'Danh sách các thủ tục hành chính đã được cập nhật với nhiều thay đổi quan trọng...',
      date: '13/07/2024',
      category: 'policy',
      image: 'https://via.placeholder.com/400x200'
    },
    {
      id: 4,
      title: 'Hội thảo về chuyển đổi số trong dịch vụ công',
      excerpt: 'Sự kiện sẽ diễn ra vào ngày 25/07/2024 tại Trung tâm Hội nghị Quốc gia...',
      date: '12/07/2024',
      category: 'event',
      image: 'https://via.placeholder.com/400x200'
    },
    {
      id: 5,
      title: 'Chính sách mới về thuế điện tử',
      excerpt: 'Bộ Tài chính vừa ban hành chính sách mới về việc áp dụng thuế điện tử...',
      date: '11/07/2024',
      category: 'policy',
      image: 'https://via.placeholder.com/400x200'
    },
    {
      id: 6,
      title: 'Thông báo bảo trì hệ thống',
      excerpt: 'Hệ thống sẽ được bảo trì từ 22:00 ngày 20/07 đến 06:00 ngày 21/07...',
      date: '10/07/2024',
      category: 'announcement',
      image: 'https://via.placeholder.com/400x200'
    }
  ];

  const filteredNews = selectedCategory === 'all' 
    ? newsData 
    : newsData.filter(news => news.category === selectedCategory);

  const featuredNews = newsData.find(news => news.featured);
  const regularNews = filteredNews.filter(news => !news.featured);

  return (
    <div className={styles.newsPage}>
      {/* Header */}
      <div className={styles.pageHeader}>
        <div className={styles.headerContent}>
          <h1 className={styles.pageTitle}>Tin Tức</h1>
          <p className={styles.pageSubtitle}>Cập nhật thông tin mới nhất từ cổng thông tin</p>
        </div>
      </div>

      <div className={styles.mainContent}>
        {/* Main Content */}
        <div className={styles.contentArea}>
          {/* Featured News */}
          {featuredNews && selectedCategory === 'all' && (
            <div className={styles.featuredSection}>
              <h2 className={styles.featuredTitle}>Tin Nổi Bật</h2>
              <article className={styles.featuredCard}>
                <img 
                  src={featuredNews.image} 
                  alt={featuredNews.title}
                  className={styles.featuredImage}
                />
                <div className={styles.featuredContent}>
                  <div className={styles.categoryTags}>
                    <span className={styles.categoryTag}>
                      {categories.find(cat => cat.id === featuredNews.category)?.name}
                    </span>
                    <span className={styles.newsDate}>{featuredNews.date}</span>
                  </div>
                  <h3 className={styles.featuredNewsTitle}>
                    {featuredNews.title}
                  </h3>
                  <p className={styles.newsExcerpt}>{featuredNews.excerpt}</p>
                  <a href="#" className={styles.readMoreBtn}>
                    Đọc thêm →
                  </a>
                </div>
              </article>
            </div>
          )}

          {/* Category Filter */}
          <div className={styles.filterSection}>
            <div className={styles.filterButtons}>
              {categories.map(category => (
                <button
                  key={category.id}
                  onClick={() => setSelectedCategory(category.id)}
                  className={`${styles.filterBtn} ${
                    selectedCategory === category.id ? styles.active : ''
                  }`}
                >
                  {category.name}
                </button>
              ))}
            </div>
          </div>

          {/* News Grid */}
          <div className={styles.newsGrid}>
            {regularNews.map(news => (
              <article key={news.id} className={styles.newsCard}>
                <img 
                  src={news.image} 
                  alt={news.title}
                  className={styles.newsImage}
                />
                <div className={styles.newsContent}>
                  <div className={styles.categoryTags}>
                    <span className={styles.categoryTag}>
                      {categories.find(cat => cat.id === news.category)?.name}
                    </span>
                    <span className={styles.newsDate}>{news.date}</span>
                  </div>
                  <h3 className={styles.newsTitle}>
                    {news.title}
                  </h3>
                  <p className={styles.newsCardExcerpt}>{news.excerpt}</p>
                  <a href="#" className={styles.readMoreBtn}>
                    Đọc thêm →
                  </a>
                </div>
              </article>
            ))}
          </div>

          {/* Pagination */}
          <div className={styles.pagination}>
            <div className={styles.paginationButtons}>
              <button className={styles.paginationBtn}>← Trước</button>
              <button className={`${styles.paginationBtn} ${styles.active}`}>1</button>
              <button className={styles.paginationBtn}>2</button>
              <button className={styles.paginationBtn}>3</button>
              <button className={styles.paginationBtn}>Sau →</button>
            </div>
          </div>
        </div>

        {/* Sidebar */}
        <div className={styles.sidebar}>
          {/* Search */}
          <div className={styles.sidebarCard}>
            <h3 className={styles.sidebarTitle}>Tìm Kiếm</h3>
            <div className="relative">
              <input
                type="text"
                placeholder="Nhập từ khóa..."
                className={styles.searchInput}
              />
              <button className={styles.searchButton}>
                🔍
              </button>
            </div>
          </div>

          {/* Popular News */}
          <div className={styles.sidebarCard}>
            <h3 className={styles.sidebarTitle}>Tin Phổ Biến</h3>
            <div className={styles.popularNews}>
              {newsData.slice(0, 5).map(news => (
                <div key={news.id} className={styles.popularItem}>
                  <img 
                    src={news.image} 
                    alt={news.title}
                    className={styles.popularImage}
                  />
                  <div className={styles.popularContent}>
                    <h4 className={styles.popularTitle}>
                      {news.title}
                    </h4>
                    <p className={styles.popularDate}>{news.date}</p>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default News;
